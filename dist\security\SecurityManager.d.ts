export interface SecuritySettings {
    enableSandbox: boolean;
    blockDangerousDownloads: boolean;
    enableCSP: boolean;
    blockMixedContent: boolean;
    enableHSTS: boolean;
    blockPlugins: boolean;
    enableMemoryProtection: boolean;
    clearDataOnExit: boolean;
}
export declare class SecurityManager {
    private settings;
    private encryptionKey;
    private secureStorage;
    constructor();
    initialize(): Promise<void>;
    private setupSecurityHeaders;
    private setupDownloadSecurity;
    private setupContentSecurity;
    private setupMemoryProtection;
    private setupProcessIsolation;
    private scanFile;
    encryptData(data: string): string;
    decryptData(encryptedData: string): string;
    secureStore(key: string, value: string): void;
    secureRetrieve(key: string): string | null;
    secureDelete(key: string): void;
    performSecurityAudit(): Promise<SecurityAuditResult>;
    clearSecurityData(): Promise<void>;
    updateSettings(newSettings: Partial<SecuritySettings>): void;
    getSettings(): SecuritySettings;
}
export interface SecurityAuditResult {
    timestamp: Date;
    issues: string[];
    recommendations: string[];
}
//# sourceMappingURL=SecurityManager.d.ts.map