import { EventEmitter } from 'events';
export interface IoTDevice {
    id: string;
    type: 'smart_tv' | 'smart_speaker' | 'security_camera' | 'smart_thermostat' | 'smart_light' | 'router' | 'smart_watch' | 'fitness_tracker' | 'smart_home_hub' | 'generic_iot';
    manufacturer: string;
    model: string;
    firmwareVersion: string;
    capabilities: IoTCapabilities;
    networkInfo: IoTNetworkInfo;
    powerInfo: IoTPowerInfo;
    location: IoTLocation;
    lastSeen: number;
    trustLevel: number;
    coordinationRole: 'coordinator' | 'participant' | 'relay' | 'observer';
}
export interface IoTCapabilities {
    networkProtocols: ('wifi' | 'ethernet' | 'zigbee' | 'zwave' | 'bluetooth' | 'lora')[];
    processingPower: 'minimal' | 'low' | 'medium' | 'high';
    memoryCapacity: number;
    storageCapacity: number;
    sensors: string[];
    actuators: string[];
    steganographicFeatures: string[];
    maxBandwidth: number;
    operatingSchedule: {
        alwaysOn: boolean;
        activeHours?: {
            start: number;
            end: number;
        };
        sleepMode: boolean;
    };
}
export interface IoTNetworkInfo {
    ipAddress: string;
    macAddress: string;
    networkType: 'wifi' | 'ethernet' | 'cellular' | 'mesh';
    signalStrength: number;
    bandwidth: {
        upload: number;
        download: number;
    };
    latency: number;
    isMetered: boolean;
    parentDevice?: string;
}
export interface IoTPowerInfo {
    powerSource: 'battery' | 'mains' | 'solar' | 'usb';
    batteryLevel?: number;
    powerConsumption: number;
    lowPowerMode: boolean;
    energyOptimization: boolean;
}
export interface IoTLocation {
    room?: string;
    floor?: string;
    building?: string;
    coordinates?: {
        x: number;
        y: number;
        z?: number;
    };
    zone: 'indoor' | 'outdoor' | 'mobile';
}
export interface IoTCoordinationTask {
    id: string;
    deviceId: string;
    type: 'network_heartbeat' | 'sensor_reading' | 'status_update' | 'firmware_check' | 'data_sync' | 'cover_traffic';
    priority: 'low' | 'medium' | 'high' | 'critical';
    powerImpact: 'minimal' | 'low' | 'medium' | 'high';
    networkUsage: number;
    duration: number;
    schedule: {
        startTime: number;
        interval?: number;
        endTime?: number;
    };
    steganographicLayer: {
        mimicNormalOperation: boolean;
        timingObfuscation: boolean;
        trafficPadding: boolean;
        protocolMimicry: string;
    };
}
export interface IoTTrafficPattern {
    deviceType: string;
    normalBehavior: {
        heartbeatInterval: number;
        dataReportingFrequency: number;
        typicalPacketSizes: number[];
        communicationProtocols: string[];
        peakActivityHours: number[];
    };
    steganographicBehavior: {
        coverTrafficGeneration: boolean;
        timingRandomization: number;
        packetSizePadding: boolean;
        protocolSwitching: boolean;
    };
}
export declare class IoTDeviceCoordinator extends EventEmitter {
    private devices;
    private activeTasks;
    private trafficPatterns;
    private meshNetwork;
    private powerManager;
    private protocolSimulator;
    private discoveryInterval;
    private coordinationInterval;
    constructor();
    initialize(): Promise<void>;
    private initializeTrafficPatterns;
    private simulateIoTDevices;
    private createSimulatedDevice;
    private getManufacturerForType;
    private getCapabilitiesForType;
    private getPowerInfoForType;
    private startDeviceDiscovery;
    private discoverNewDevices;
    private updateDeviceStatus;
    private startCoordinationService;
    private executeScheduledTasks;
    private executeIoTTask;
    private performIoTActivity;
    private sendHeartbeat;
    private reportSensorData;
    private generateSensorData;
    private sendStatusUpdate;
    private calculateNetworkQuality;
    private checkFirmwareUpdate;
    private syncDeviceData;
    private generateDeviceCoverTraffic;
    private generateCoverTraffic;
    private isInActiveHours;
    scheduleCoordinatedIoTActivity(deviceIds: string[], activityType: string, startTime: number): Promise<string>;
    getIoTDevices(): IoTDevice[];
    getDevicesByType(type: IoTDevice['type']): IoTDevice[];
    getCoordinationStatus(): {
        totalDevices: number;
        activeDevices: number;
        activeTasks: number;
        powerOptimizedDevices: number;
        networkQuality: number;
    };
    destroy(): void;
}
//# sourceMappingURL=IoTDeviceCoordinator.d.ts.map