{"version": 3, "file": "BiometricMimicry.d.ts", "sourceRoot": "", "sources": ["../../src/biometric/BiometricMimicry.ts"], "names": [], "mappings": "AAGA,MAAM,WAAW,gBAAgB;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,aAAa,EAAE;QACX,cAAc,EAAE,cAAc,CAAC;QAC/B,aAAa,EAAE,aAAa,CAAC;QAC7B,cAAc,EAAE,cAAc,CAAC;QAC/B,eAAe,EAAE,eAAe,CAAC;QACjC,aAAa,EAAE,aAAa,CAAC;KAChC,CAAC;IACF,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;CAC1B;AAED,MAAM,WAAW,cAAc;IAC3B,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,YAAY,EAAE;QACV,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;KACvB,CAAC;IACF,aAAa,EAAE;QACX,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,eAAe,EAAE,MAAM,CAAC;QACxB,gBAAgB,EAAE,WAAW,GAAG,gBAAgB,GAAG,QAAQ,CAAC;KAC/D,CAAC;IACF,gBAAgB,EAAE;QACd,WAAW,EAAE,MAAM,CAAC;QACpB,gBAAgB,EAAE,MAAM,CAAC;QACzB,cAAc,EAAE,OAAO,GAAG,SAAS,GAAG,UAAU,CAAC;KACpD,CAAC;CACL;AAED,MAAM,WAAW,aAAa;IAC1B,eAAe,EAAE;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,iBAAiB,EAAE,MAAM,EAAE,CAAC;QAC5B,iBAAiB,EAAE,MAAM,EAAE,CAAC;KAC/B,CAAC;IACF,yBAAyB,EAAE;QACvB,YAAY,EAAE,MAAM,CAAC;QACrB,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,aAAa,EAAE;QACX,aAAa,EAAE,MAAM,CAAC;QACtB,gBAAgB,EAAE,MAAM,CAAC;QACzB,YAAY,EAAE,QAAQ,GAAG,OAAO,GAAG,UAAU,CAAC;KACjD,CAAC;IACF,cAAc,EAAE;QACZ,WAAW,EAAE,MAAM,CAAC;QACpB,kBAAkB,EAAE,OAAO,CAAC;QAC5B,eAAe,EAAE,SAAS,GAAG,aAAa,CAAC;KAC9C,CAAC;CACL;AAED,MAAM,WAAW,cAAc;IAC3B,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,QAAQ,GAAG,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC;IACjE,UAAU,EAAE;QACR,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;QACb,UAAU,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,uBAAuB,EAAE;QACrB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,kBAAkB,EAAE,OAAO,CAAC;QAC5B,gBAAgB,EAAE,MAAM,CAAC;KAC5B,CAAC;CACL;AAED,MAAM,WAAW,eAAe;IAC5B,eAAe,EAAE,YAAY,GAAG,aAAa,GAAG,eAAe,GAAG,QAAQ,CAAC;IAC3E,QAAQ,EAAE;QACN,OAAO,EAAE,MAAM,CAAC;QAChB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,eAAe,EAAE,SAAS,GAAG,eAAe,GAAG,QAAQ,CAAC;KAC3D,CAAC;IACF,cAAc,EAAE;QACZ,WAAW,EAAE,MAAM,CAAC;QACpB,iBAAiB,EAAE,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC;QAC/D,iBAAiB,EAAE,UAAU,GAAG,YAAY,GAAG,cAAc,CAAC;KACjE,CAAC;IACF,aAAa,EAAE;QACX,SAAS,EAAE,MAAM,CAAC;QAClB,YAAY,EAAE,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;QAC1C,cAAc,EAAE,SAAS,GAAG,YAAY,GAAG,MAAM,CAAC;KACrD,CAAC;CACL;AAED,MAAM,WAAW,aAAa;IAC1B,eAAe,EAAE,MAAM,CAAC;IACxB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,yBAAyB,EAAE,MAAM,CAAC;IAClC,UAAU,EAAE;QACR,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,aAAa,EAAE;QACX,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,QAAQ,EAAE,MAAM,EAAE,CAAC;KACtB,CAAC;CACL;AAED,qBAAa,gBAAgB;IACzB,OAAO,CAAC,QAAQ,CAA4C;IAC5D,OAAO,CAAC,cAAc,CAAiC;IACvD,OAAO,CAAC,YAAY,CAAiB;IACrC,OAAO,CAAC,iBAAiB,CAAuD;IAChF,OAAO,CAAC,aAAa,CAA6D;;IAM5E,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAOjC,OAAO,CAAC,sBAAsB;YA6MhB,qBAAqB;IAMnC,OAAO,CAAC,oBAAoB;IAiB5B,OAAO,CAAC,sBAAsB;IAY9B,OAAO,CAAC,oBAAoB;IAkB5B,OAAO,CAAC,8BAA8B;IAqCtC,OAAO,CAAC,YAAY;IAsBpB,OAAO,CAAC,eAAe;IAgDvB,OAAO,CAAC,gBAAgB;IAMxB,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAmDlC,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI;IAyD7D,iBAAiB,IAAI,gBAAgB,GAAG,IAAI;IAI5C,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAUzC,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,GAAG,MAAM;IAe7D,oBAAoB,IAAI;QACpB,aAAa,EAAE,MAAM,CAAC;QACtB,cAAc,EAAE,MAAM,CAAC;QACvB,eAAe,EAAE,MAAM,CAAC;QACxB,YAAY,EAAE,OAAO,CAAC;QACtB,UAAU,EAAE,MAAM,CAAC;KACtB;IAUD,kBAAkB,IAAI,IAAI;IAK1B,mBAAmB,IAAI,IAAI;CAI9B"}