export interface BiometricProfile {
    id: string;
    name: string;
    biometricData: {
        typingDynamics: TypingDynamics;
        mouseMovement: MouseMovement;
        readingPattern: ReadingPattern;
        navigationStyle: NavigationStyle;
        attentionSpan: AttentionSpan;
    };
    confidence: number;
    lastUpdated: number;
    adaptationRate: number;
}
export interface TypingDynamics {
    keystrokeTimings: number[];
    interKeyIntervals: number[];
    typingRhythm: {
        burstLength: number;
        pauseLength: number;
        consistency: number;
    };
    errorPatterns: {
        commonErrors: string[];
        correctionDelay: number;
        correctionMethod: 'backspace' | 'select_replace' | 'ignore';
    };
    pressureDynamics: {
        avgPressure: number;
        pressureVariance: number;
        releasePattern: 'quick' | 'gradual' | 'variable';
    };
}
export interface MouseMovement {
    velocityProfile: {
        avgVelocity: number;
        accelerationCurve: number[];
        decelerationCurve: number[];
    };
    trajectoryCharacteristics: {
        straightness: number;
        overshoot: number;
        tremor: number;
    };
    clickPatterns: {
        clickDuration: number;
        doubleClickSpeed: number;
        dragBehavior: 'smooth' | 'jerky' | 'hesitant';
    };
    scrollBehavior: {
        scrollSpeed: number;
        scrollAcceleration: boolean;
        scrollDirection: 'natural' | 'traditional';
    };
}
export interface ReadingPattern {
    readingSpeed: number;
    scanningPattern: 'linear' | 'f_pattern' | 'z_pattern' | 'random';
    focusAreas: {
        headlines: number;
        images: number;
        text: number;
        navigation: number;
    };
    comprehensionIndicators: {
        backtrackFrequency: number;
        pauseAtPunctuation: boolean;
        skimmingBehavior: number;
    };
}
export interface NavigationStyle {
    browsingPattern: 'methodical' | 'exploratory' | 'goal_oriented' | 'random';
    tabUsage: {
        maxTabs: number;
        tabSwitchFrequency: number;
        tabOrganization: 'grouped' | 'chronological' | 'random';
    };
    searchBehavior: {
        queryLength: number;
        refinementPattern: 'iterative' | 'single_shot' | 'exploratory';
        resultExamination: 'thorough' | 'quick_scan' | 'first_result';
    };
    bookmarkUsage: {
        frequency: number;
        organization: 'folders' | 'tags' | 'none';
        revisitPattern: 'regular' | 'occasional' | 'rare';
    };
}
export interface AttentionSpan {
    sessionDuration: number;
    taskSwitchingRate: number;
    distractionSusceptibility: number;
    focusDepth: {
        shallow: number;
        medium: number;
        deep: number;
    };
    breakPatterns: {
        frequency: number;
        duration: number;
        triggers: string[];
    };
}
export declare class BiometricMimicry {
    private profiles;
    private currentProfile;
    private learningMode;
    private adaptationHistory;
    private biometricData;
    constructor();
    initialize(): Promise<void>;
    private initializeBaseProfiles;
    private loadBiometricProfiles;
    private selectOptimalProfile;
    private startBiometricLearning;
    private collectBiometricData;
    private generateSyntheticBiometricData;
    private adaptProfile;
    private analyzeAndAdapt;
    private applyAdaptations;
    simulateTyping(text: string): void;
    simulateMouseMovement(targetX: number, targetY: number): void;
    getCurrentProfile(): BiometricProfile | null;
    switchProfile(profileId: string): boolean;
    createCustomProfile(name: string, biometricData: any): string;
    getProfileStatistics(): {
        totalProfiles: number;
        currentProfile: string;
        adaptationCount: number;
        learningMode: boolean;
        dataPoints: number;
    };
    enableLearningMode(): void;
    disableLearningMode(): void;
}
//# sourceMappingURL=BiometricMimicry.d.ts.map