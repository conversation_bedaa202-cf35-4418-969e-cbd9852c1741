export interface Block {
    index: number;
    timestamp: number;
    data: CoordinationData;
    previousHash: string;
    hash: string;
    nonce: number;
    validator: string;
}
export interface CoordinationData {
    type: 'pattern_proposal' | 'pattern_execution' | 'node_join' | 'node_leave' | 'consensus_vote';
    nodeId: string;
    payload: any;
    signature: string;
}
export interface ConsensusVote {
    blockHash: string;
    nodeId: string;
    vote: 'approve' | 'reject';
    timestamp: number;
    signature: string;
}
export interface NetworkConsensus {
    blockHash: string;
    votes: ConsensusVote[];
    status: 'pending' | 'approved' | 'rejected';
    threshold: number;
    finalizedAt?: number;
}
export declare class BlockchainCoordinator {
    private chain;
    private pendingTransactions;
    private consensusVotes;
    private nodeId;
    private privateKey;
    private validators;
    private difficulty;
    constructor(nodeId: string, privateKey: Buffer);
    private createGenesisBlock;
    addTransaction(data: CoordinationData): void;
    private verifySignature;
    mineBlock(): Promise<Block | null>;
    private mineBlockWithProofOfWork;
    private calculateHash;
    proposeBlock(block: Block): Promise<boolean>;
    voteOnBlock(blockHash: string, vote: 'approve' | 'reject'): Promise<void>;
    private signVote;
    private checkConsensus;
    private finalizeBlock;
    private isValidBlock;
    getLatestBlock(): Block;
    getChain(): Block[];
    isChainValid(): boolean;
    addValidator(nodeId: string): void;
    removeValidator(nodeId: string): void;
    getValidators(): string[];
    getPendingTransactions(): CoordinationData[];
    getConsensusStatus(blockHash: string): NetworkConsensus | null;
    proposePatternExecution(patternId: string, participants: string[], startTime: number): Promise<string>;
    private signCoordinationData;
    announceNodeJoin(nodeCapabilities: any): Promise<void>;
    announceNodeLeave(reason: string): Promise<void>;
    getNetworkState(): {
        chainLength: number;
        pendingTransactions: number;
        validators: number;
        pendingConsensus: number;
        isValid: boolean;
    };
    cleanupOldConsensus(): void;
}
//# sourceMappingURL=BlockchainCoordinator.d.ts.map