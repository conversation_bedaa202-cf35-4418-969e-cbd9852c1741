{"version": 3, "file": "ProxyManager.js", "sourceRoot": "", "sources": ["../../src/network/ProxyManager.ts"], "names": [], "mappings": ";;;AAAA,uCAAmC;AA0BnC,MAAa,YAAY;IAOrB;QANQ,iBAAY,GAAuB,IAAI,CAAC;QACxC,cAAS,GAAkB,EAAE,CAAC;QAC9B,oBAAe,GAAY,KAAK,CAAC;QACjC,qBAAgB,GAA0B,IAAI,CAAC;QAC/C,cAAS,GAAqB,IAAI,CAAC;QAGvC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;IAC/B,CAAC;IAEO,wBAAwB;QAC5B,wCAAwC;QACxC,IAAI,CAAC,SAAS,GAAG;YACb;gBACI,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,CAAC;gBACP,OAAO,EAAE,IAAI;aAChB;YACD,6BAA6B;YAC7B;gBACI,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,KAAK;aACjB;YACD,6BAA6B;YAC7B;gBACI,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,KAAK;aACjB;SACJ,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,sEAAsE;QACtE,oFAAoF;QACpF,MAAM,WAAW,GAAkB;YAC/B;gBACI,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,KAAK;aACjB;YACD;gBACI,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,KAAK;aACjB;SACJ,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAmB;QAC9B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAE3B,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,IAAI,CAAC;YACD,IAAI,UAAU,GAAG,EAAE,CAAC;YAEpB,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO;oBACR,UAAU,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;oBAC5D,MAAM;gBACV,KAAK,QAAQ,CAAC;gBACd,KAAK,QAAQ;oBACT,UAAU,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;oBAC5D,MAAM;YACd,CAAC;YAED,MAAM,GAAG,CAAC,QAAQ,CAAC;gBACf,UAAU;gBACV,gBAAgB,EAAE,6BAA6B;aAClD,CAAC,CAAC;YAEH,gEAAgE;YAEhE,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QACnC,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAmB;QAC/B,IAAI,CAAC;YACD,0BAA0B;YAC1B,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACvD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACJ,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAmB;QAC5C,IAAI,CAAC;YACD,8BAA8B;YAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;gBAE9D,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;oBACtB,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBACpB,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;gBAEH,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;oBACzB,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAAmB;QAC3C,6BAA6B;QAC7B,gFAAgF;QAChF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAE9D,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;gBACtB,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBACpB,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;gBACzB,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,WAAW;QACb,MAAM,cAAc,GAAkB,EAAE,CAAC;QAEzC,yCAAyC;QACzC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjC,IAAI,KAAK,CAAC,OAAO,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/C,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAClE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,gCAAgC;QAChC,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;QACtF,IAAI,WAAW,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,kBAA0B,EAAE;QAC5C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3C,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7B,CAAC,EAAE,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEhC,OAAO,CAAC,GAAG,CAAC,2BAA2B,eAAe,oBAAoB,CAAC,CAAC;IAChF,CAAC;IAED,oBAAoB;QAChB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAC3C,CAAC;IAED,QAAQ,CAAC,MAAmB;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,IAAY;QAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,YAAY;QACR,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,2BAA2B;QAC3B,MAAM,GAAG,CAAC,QAAQ,CAAC;YACf,IAAI,EAAE,YAAY;YAClB,SAAS,EAAE;;;;;;;;aAQV;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,uBAAuB;QACzB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,8DAA8D;QAC9D,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC3E,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,2BAA2B;YAE9D,UAAU,CAAC,GAAG,EAAE;gBACZ,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAChC,CAAC,EAAE,KAAK,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,2CAA2C;QAC3C,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC;YAEvC,2BAA2B;YAC3B,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC;YAE1E,+BAA+B;YAC/B,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC;YAErE,QAAQ,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,+BAA+B;IAC/B,KAAK,CAAC,UAAU,CAAC,MAAiB;QAC9B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QAExB,kEAAkE;QAClE,+DAA+D;QAC/D,OAAO,CAAC,GAAG,CAAC,6BAA6B,MAAM,CAAC,QAAQ,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACnF,CAAC;IAED,KAAK,CAAC,aAAa;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACpC,CAAC;IAED,YAAY;QACR,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI;YAClC,MAAM,EAAE,IAAI,CAAC,SAAS,IAAI,SAAS;SACtC,CAAC;IACN,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;IACL,CAAC;CACJ;AAlTD,oCAkTC"}