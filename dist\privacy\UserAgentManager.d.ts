export interface UserAgentProfile {
    userAgent: string;
    platform: string;
    browser: string;
    version: string;
    os: string;
    device: string;
    acceptLanguage: string;
    acceptEncoding: string;
    acceptCharset: string;
}
export declare class UserAgentManager {
    private profiles;
    private currentProfile;
    private rotationEnabled;
    private rotationInterval;
    constructor();
    initialize(): Promise<void>;
    private generateUserAgentProfiles;
    private generateRandomProfile;
    private setupUserAgentRotation;
    private applyCurrentProfile;
    private getAcceptHeader;
    private generateSecChUa;
    private getPlatformName;
    rotateUserAgent(): void;
    setProfile(profile: UserAgentProfile): void;
    getCurrentProfile(): UserAgentProfile;
    enableRotation(): void;
    disableRotation(): void;
    getAvailableProfiles(): UserAgentProfile[];
    addCustomProfile(profile: UserAgentProfile): void;
    destroy(): void;
}
//# sourceMappingURL=UserAgentManager.d.ts.map