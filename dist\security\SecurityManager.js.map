{"version": 3, "file": "SecurityManager.js", "sourceRoot": "", "sources": ["../../src/security/SecurityManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAuD;AACvD,+CAAiC;AACjC,uCAAyB;AAczB,MAAa,eAAe;IAKxB;QACI,IAAI,CAAC,QAAQ,GAAG;YACZ,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,IAAI;YACvB,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;YAClB,sBAAsB,EAAE,IAAI;YAC5B,eAAe,EAAE,IAAI;SACxB,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAEO,oBAAoB;QACxB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC7E,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;YAEtD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC1B,eAAe,CAAC,yBAAyB,CAAC,GAAG;oBACzC,sBAAsB;wBACtB,mDAAmD;wBACnD,oCAAoC;wBACpC,+BAA+B;wBAC/B,6BAA6B;wBAC7B,yBAAyB;wBACzB,qBAAqB;wBACrB,oBAAoB;wBACpB,mBAAmB;iBACtB,CAAC;YACN,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;gBAC3B,eAAe,CAAC,2BAA2B,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;YAC3F,CAAC;YAED,mBAAmB;YACnB,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9C,eAAe,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACxD,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACxD,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YACzE,eAAe,CAAC,oBAAoB,CAAC,GAAG;gBACpC,gEAAgE;oBAChE,4EAA4E;oBAC5E,uEAAuE;aAC1E,CAAC;YAEF,4BAA4B;YAC5B,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO,eAAe,CAAC,cAAc,CAAC,CAAC;YAEvC,QAAQ,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,qBAAqB;QACzB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,GAAG,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE;YACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC;gBACzC,OAAO;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,mBAAmB,GAAG;gBACxB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;gBAC7D,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;aACzD,CAAC;YAEF,MAAM,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC/C,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CACvC,CAAC;YAEF,IAAI,WAAW,EAAE,CAAC;gBACd,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;gBACxD,OAAO;YACX,CAAC;YAED,yCAAyC;YACzC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBAC7B,IAAI,KAAK,KAAK,WAAW,EAAE,CAAC;oBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBACtC,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,sBAAsB;QACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAClC,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;gBACzE,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC;gBACjC,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC5C,OAAO,CAAC,IAAI,CAAC,0BAA0B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;oBACtD,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC3B,OAAO;gBACX,CAAC;gBACD,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACP,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC7B,GAAG,CAAC,2BAA2B,CAAC,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE;gBAClE,MAAM,kBAAkB,GAAG;oBACvB,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa;iBAC5C,CAAC;gBACF,QAAQ,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,4BAA4B;QAC5B,cAAG,CAAC,0BAA0B,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;QAE7C,GAAG,CAAC,QAAQ,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC7D,gCAAgC;YAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc;QAC3C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,qBAAqB;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC;YACxC,OAAO;QACX,CAAC;QAED,oCAAoC;QACpC,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC1E,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAAC;QAClF,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;QACxD,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAE5D,uBAAuB;QACvB,WAAW,CAAC,GAAG,EAAE;YACb,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,WAAW,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ;gBACpD,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;gBAC1E,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;oBACZ,MAAM,CAAC,EAAE,EAAE,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC,EAAE,KAAK,CAAC,CAAC;IACd,CAAC;IAEO,qBAAqB;QACzB,wBAAwB;QACxB,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QACnD,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;QAEpE,gCAAgC;QAChC,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;QAEzE,mDAAmD;QACnD,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;QAExE,+BAA+B;QAC/B,cAAG,CAAC,WAAW,CAAC,YAAY,CAAC,wCAAwC,CAAC,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,QAAgB;QACnC,IAAI,CAAC;YACD,iEAAiE;YACjE,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpC,gDAAgD;YAChD,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,QAAQ;gBAC1C,OAAO,CAAC,IAAI,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;gBACjD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,uBAAuB;YACvB,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAEzC,0CAA0C;YAC1C,MAAM,mBAAmB,GAAG;gBACxB,MAAM,EAAE,gBAAgB;gBACxB,UAAU,EAAE,iBAAiB;aAChC,CAAC;YAEF,MAAM,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC/C,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAC5B,CAAC;YAEF,IAAI,WAAW,EAAE,CAAC;gBACd,OAAO,CAAC,IAAI,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;gBACxD,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB;gBAC3C,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,yBAAyB;IACzB,WAAW,CAAC,IAAY;QACpB,MAAM,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACnD,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACjC,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC;IAChD,CAAC;IAED,WAAW,CAAC,aAAqB;QAC7B,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1E,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC1D,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,WAAW,CAAC,GAAW,EAAE,KAAa;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED,cAAc,CAAC,GAAW;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED,YAAY,CAAC,GAAW;QACpB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,yBAAyB;IACzB,KAAK,CAAC,oBAAoB;QACtB,MAAM,MAAM,GAAwB;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACtB,CAAC;QAEF,4BAA4B;QAC5B,MAAM,OAAO,GAAG,wBAAa,CAAC,aAAa,EAAE,CAAC;QAC9C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;YAEvC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,EAAE,CAAC;gBAClC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,yBAAyB;QACzB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QACnC,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAE1C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1D,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;YACnC,MAAM,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAC7B,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,GAAG,CAAC,cAAc,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED,cAAc,CAAC,WAAsC;QACjD,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;IACzD,CAAC;IAED,WAAW;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;CACJ;AArTD,0CAqTC"}