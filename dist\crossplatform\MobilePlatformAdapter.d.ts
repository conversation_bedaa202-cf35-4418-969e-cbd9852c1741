import { EventEmitter } from 'events';
export interface MobileDeviceInfo {
    deviceId: string;
    platform: 'android' | 'ios';
    osVersion: string;
    deviceModel: string;
    screenResolution: {
        width: number;
        height: number;
    };
    batteryLevel: number;
    networkType: 'wifi' | 'cellular' | '5g' | '4g' | '3g';
    location: {
        latitude?: number;
        longitude?: number;
        accuracy?: number;
        timestamp: number;
    };
    capabilities: {
        sensors: string[];
        networkCapabilities: string[];
        appPermissions: string[];
        steganographicFeatures: string[];
    };
}
export interface MobileCoordinationTask {
    id: string;
    type: 'web_browsing' | 'app_switching' | 'background_sync' | 'sensor_simulation' | 'network_activity';
    priority: 'low' | 'medium' | 'high' | 'critical';
    batteryImpact: 'minimal' | 'low' | 'medium' | 'high';
    networkUsage: number;
    duration: number;
    parameters: {
        targets?: string[];
        frequency: number;
        variance: number;
        steganographicLayer: {
            coverTraffic: boolean;
            timingObfuscation: boolean;
            patternMasking: boolean;
            batteryOptimized: boolean;
        };
    };
}
export interface MobileAppProfile {
    packageName: string;
    appName: string;
    category: 'social' | 'productivity' | 'entertainment' | 'news' | 'shopping' | 'utility';
    usagePattern: {
        averageSessionDuration: number;
        dailyUsageMinutes: number;
        peakUsageHours: number[];
        backgroundActivity: boolean;
    };
    networkBehavior: {
        dataUsagePerSession: number;
        requestFrequency: number;
        typicalEndpoints: string[];
    };
}
export interface MobileSensorData {
    accelerometer?: {
        x: number;
        y: number;
        z: number;
    };
    gyroscope?: {
        x: number;
        y: number;
        z: number;
    };
    magnetometer?: {
        x: number;
        y: number;
        z: number;
    };
    gps?: {
        latitude: number;
        longitude: number;
        accuracy: number;
    };
    proximity?: number;
    lightSensor?: number;
    timestamp: number;
}
export declare class MobilePlatformAdapter extends EventEmitter {
    private deviceInfo;
    private appProfiles;
    private activeTasks;
    private sensorSimulator;
    private batteryManager;
    private networkManager;
    constructor();
    initialize(): Promise<void>;
    private detectMobileDevice;
    private initializeAppProfiles;
    executeMobileCoordinationTask(task: MobileCoordinationTask): Promise<void>;
    private simulateWebBrowsing;
    private simulateAppSwitching;
    private simulateAppUsage;
    private simulateBackgroundSync;
    private simulateSensorActivity;
    private transmitSensorData;
    private simulateNetworkActivity;
    private getMobileUserAgent;
    private startBatteryMonitoring;
    private startNetworkMonitoring;
    private startLocationTracking;
    getMobileDeviceInfo(): MobileDeviceInfo;
    getActiveTaskCount(): number;
    getBatteryOptimizationStatus(): {
        batteryLevel: number;
        optimizationEnabled: boolean;
        taskExecutionAllowed: boolean;
    };
    destroy(): void;
}
//# sourceMappingURL=MobilePlatformAdapter.d.ts.map