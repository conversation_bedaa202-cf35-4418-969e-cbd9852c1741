export interface PrivacySettings {
    blockTrackers: boolean;
    blockAds: boolean;
    blockFingerprinting: boolean;
    spoofTimezone: boolean;
    spoofLanguage: boolean;
    spoofScreen: boolean;
    randomizeCanvasFingerprint: boolean;
    blockWebRTC: boolean;
    clearCookiesOnExit: boolean;
    useDoH: boolean;
}
export declare class PrivacyManager {
    private settings;
    private trackingDomains;
    private adBlockRules;
    constructor();
    initialize(): Promise<void>;
    private loadBlockLists;
    private configureSession;
    private setupRequestInterception;
    private setupScriptInjection;
    private isTrackingDomain;
    private matchesAdBlockRule;
    private spoofReferrer;
    private generateRandomUserAgent;
    private generatePACScript;
    private generateProtectionScript;
    updateSettings(newSettings: Partial<PrivacySettings>): void;
    getSettings(): PrivacySettings;
    clearPrivacyData(): Promise<void>;
}
//# sourceMappingURL=PrivacyManager.d.ts.map