import { EventEmitter } from 'events';
export interface QuantumBit {
    value: 0 | 1;
    basis: 'rectilinear' | 'diagonal';
    polarization: number;
    measured: boolean;
    timestamp: number;
}
export interface BB84Session {
    id: string;
    participants: [string, string];
    status: 'initializing' | 'transmission' | 'sifting' | 'error_correction' | 'privacy_amplification' | 'complete' | 'aborted';
    rawKey: QuantumBit[];
    siftedKey: Buffer;
    finalKey: Buffer;
    errorRate: number;
    securityParameter: number;
    startTime: number;
    endTime?: number;
}
export interface QuantumChannel {
    id: string;
    participants: string[];
    noiseLevel: number;
    eavesdroppingDetected: boolean;
    transmissionCount: number;
    lastUsed: number;
    isAuthenticated: boolean;
}
export interface QKDStatistics {
    sessionsCompleted: number;
    totalBitsTransmitted: number;
    averageErrorRate: number;
    eavesdroppingAttempts: number;
    keyGenerationRate: number;
    channelEfficiency: number;
}
export declare class QuantumKeyDistribution extends EventEmitter {
    private sessions;
    private channels;
    private statistics;
    private isActive;
    constructor();
    initialize(): Promise<void>;
    createQuantumChannel(channelId: string, participants: string[]): Promise<QuantumChannel>;
    startBB84Session(alice: string, bob: string, keyLength?: number): Promise<string>;
    private executeQuantumTransmission;
    private prepareQuantumBit;
    private transmitQuantumBit;
    private measureQuantumBit;
    private performSifting;
    private performErrorCorrection;
    private cascadeErrorCorrection;
    private performPrivacyAmplification;
    private calculateInformationLeakage;
    private universalHash;
    private getDefaultChannel;
    private updateStatistics;
    getSession(sessionId: string): BB84Session | undefined;
    getSessionKey(sessionId: string): Buffer | null;
    getStatistics(): QKDStatistics;
    getChannelStatus(): Array<{
        id: string;
        participants: string[];
        noiseLevel: number;
        eavesdroppingDetected: boolean;
        transmissionCount: number;
        isActive: boolean;
    }>;
    simulateQuantumCommunication(peerId: string, keyLength?: number): Promise<Buffer>;
    cleanupExpiredSessions(): void;
    destroy(): void;
}
//# sourceMappingURL=QuantumKeyDistribution.d.ts.map