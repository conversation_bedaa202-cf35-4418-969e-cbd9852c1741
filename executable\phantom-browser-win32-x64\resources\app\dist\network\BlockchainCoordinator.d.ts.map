{"version": 3, "file": "BlockchainCoordinator.d.ts", "sourceRoot": "", "sources": ["../../src/network/BlockchainCoordinator.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,KAAK;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,gBAAgB,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;IACrB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,gBAAgB;IAC7B,IAAI,EAAE,kBAAkB,GAAG,mBAAmB,GAAG,WAAW,GAAG,YAAY,GAAG,gBAAgB,CAAC;IAC/F,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,GAAG,CAAC;IACb,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,aAAa;IAC1B,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,SAAS,GAAG,QAAQ,CAAC;IAC3B,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,gBAAgB;IAC7B,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,aAAa,EAAE,CAAC;IACvB,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC;IAC5C,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,qBAAa,qBAAqB;IAC9B,OAAO,CAAC,KAAK,CAAe;IAC5B,OAAO,CAAC,mBAAmB,CAA0B;IACrD,OAAO,CAAC,cAAc,CAA4C;IAClE,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,UAAU,CAA0B;IAC5C,OAAO,CAAC,UAAU,CAAa;gBAEnB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;IAM9C,OAAO,CAAC,kBAAkB;IAuB1B,cAAc,CAAC,IAAI,EAAE,gBAAgB,GAAG,IAAI;IAU5C,OAAO,CAAC,eAAe;IAMjB,SAAS,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YAyB1B,wBAAwB;IAoBtC,OAAO,CAAC,aAAa;IAKf,YAAY,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC;IA0B5C,WAAW,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IA4B/E,OAAO,CAAC,QAAQ;YAKF,cAAc;YAmBd,aAAa;IAa3B,OAAO,CAAC,YAAY;IA2BpB,cAAc,IAAI,KAAK;IAIvB,QAAQ,IAAI,KAAK,EAAE;IAInB,YAAY,IAAI,OAAO;IAiBvB,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAKlC,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAKrC,aAAa,IAAI,MAAM,EAAE;IAIzB,sBAAsB,IAAI,gBAAgB,EAAE;IAI5C,kBAAkB,CAAC,SAAS,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI;IAKxD,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAyB5G,OAAO,CAAC,oBAAoB;IAKtB,gBAAgB,CAAC,gBAAgB,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IActD,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IActD,eAAe,IAAI;QACf,WAAW,EAAE,MAAM,CAAC;QACpB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,UAAU,EAAE,MAAM,CAAC;QACnB,gBAAgB,EAAE,MAAM,CAAC;QACzB,OAAO,EAAE,OAAO,CAAC;KACpB;IAWD,mBAAmB,IAAI,IAAI;CAS9B"}