{"version": 3, "file": "HybridCryptoSystem.d.ts", "sourceRoot": "", "sources": ["../../src/quantum/HybridCryptoSystem.ts"], "names": [], "mappings": "AAIA,MAAM,WAAW,aAAa;IAC1B,SAAS,EAAE;QACP,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;QACnB,SAAS,EAAE,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;KACvC,CAAC;IACF,WAAW,EAAE;QACT,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;QACnB,SAAS,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,CAAC;KAChD,CAAC;IACF,QAAQ,EAAE;QACN,SAAS,EAAE,MAAM,CAAC;QAClB,WAAW,EAAE,MAAM,CAAC;KACvB,CAAC;CACL;AAED,MAAM,WAAW,sBAAsB;IACnC,mBAAmB,EAAE,MAAM,CAAC;IAC5B,qBAAqB,EAAE,MAAM,CAAC;IAC9B,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE;QACN,UAAU,EAAE,MAAM,EAAE,CAAC;QACrB,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,aAAa,EAAE,MAAM,CAAC;KACzB,CAAC;CACL;AAED,MAAM,WAAW,mBAAmB;IAChC,KAAK,EAAE,WAAW,GAAG,QAAQ,GAAG,cAAc,CAAC;IAC/C,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE;QACR,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,MAAM,EAAE,MAAM,EAAE,CAAC;QACjB,UAAU,EAAE,MAAM,EAAE,CAAC;KACxB,CAAC;IACF,iBAAiB,EAAE,SAAS,GAAG,WAAW,GAAG,aAAa,CAAC;CAC9D;AAED,MAAM,WAAW,kBAAkB;IAC/B,aAAa,EAAE,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IAC/D,iBAAiB,EAAE,MAAM,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,eAAe,EAAE,MAAM,CAAC;IACxB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,UAAU,EAAE,MAAM,CAAC;CACtB;AAED,qBAAa,kBAAkB;IAC3B,OAAO,CAAC,GAAG,CAAyB;IACpC,OAAO,CAAC,aAAa,CAAsB;IAC3C,OAAO,CAAC,kBAAkB,CAAqB;IAC/C,OAAO,CAAC,QAAQ,CAAyC;IACzD,OAAO,CAAC,kBAAkB,CAKxB;;IAqCI,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAW3B,qBAAqB,CAAC,aAAa,GAAE,CAAC,GAAG,CAAC,GAAG,CAAK,GAAG,OAAO,CAAC,aAAa,CAAC;YAmCnE,wBAAwB;YAmBxB,0BAA0B;IAmBxC,OAAO,CAAC,iBAAiB;IAgBzB,OAAO,CAAC,oBAAoB;IAItB,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,sBAAsB,CAAC;IAyCxH,aAAa,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,cAAc,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;IAyC7G,OAAO,CAAC,oBAAoB;YAoBd,gBAAgB;YAmBhB,kBAAkB;IAUhC,OAAO,CAAC,oBAAoB;YASd,gBAAgB;YAShB,kBAAkB;IAQ1B,yBAAyB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAmBlD,yBAAyB;IAsBvC,OAAO,CAAC,mBAAmB;IAU3B,OAAO,CAAC,0BAA0B;IAuBlC,OAAO,CAAC,4BAA4B;IAWpC,OAAO,CAAC,+BAA+B;IA4BvC,OAAO,CAAC,wBAAwB;IAqBhC,SAAS,IAAI;QACT,aAAa,EAAE,mBAAmB,CAAC;QACnC,kBAAkB,EAAE,kBAAkB,CAAC;QACvC,kBAAkB,EAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnD,UAAU,EAAE,MAAM,CAAC;KACtB;IASD,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAK7D,OAAO,IAAI,IAAI;CAKlB"}