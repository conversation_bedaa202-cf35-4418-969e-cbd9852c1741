import { EventEmitter } from 'events';
import * as net from 'net';
export interface PeerConnection {
    id: string;
    socket: net.Socket;
    address: string;
    port: number;
    lastSeen: number;
    latency: number;
    bandwidth: number;
    isInbound: boolean;
    handshakeComplete: boolean;
}
export interface P2PMessage {
    type: 'handshake' | 'ping' | 'pong' | 'data' | 'discovery' | 'disconnect';
    nodeId: string;
    timestamp: number;
    payload: any;
    signature: string;
}
export interface NetworkDiscovery {
    knownPeers: string[];
    bootstrapNodes: string[];
    discoveryMethods: ('dht' | 'bootstrap' | 'peer_exchange' | 'mdns')[];
}
export interface BandwidthStats {
    bytesReceived: number;
    bytesSent: number;
    messagesReceived: number;
    messagesSent: number;
    averageLatency: number;
    peakBandwidth: number;
}
export declare class P2PNetworking extends EventEmitter {
    private nodeId;
    private privateKey;
    private publicKey;
    private server;
    private peers;
    private maxPeers;
    private port;
    private isListening;
    private bandwidthStats;
    private discoveryInterval;
    private maintenanceInterval;
    constructor(nodeId: string, privateKey: Buffer, publicKey: Buffer, port?: number);
    initialize(): Promise<void>;
    private setupServerHandlers;
    private startListening;
    private handleIncomingConnection;
    connectToPeer(address: string, port: number): Promise<boolean>;
    private setupPeerHandlers;
    private handlePeerData;
    private handlePeerMessage;
    private handleHandshake;
    private handlePing;
    private handlePong;
    private handleDataMessage;
    private handleDiscoveryMessage;
    private handleDisconnectMessage;
    private sendHandshake;
    private startPingPong;
    sendMessage(peer: PeerConnection, message: P2PMessage): void;
    broadcastMessage(message: P2PMessage): void;
    private signMessage;
    private verifyMessageSignature;
    private disconnectPeer;
    private startPeerDiscovery;
    private performPeerDiscovery;
    private startNetworkMaintenance;
    private performNetworkMaintenance;
    private updateAverageLatency;
    private updateBandwidthStats;
    getConnectedPeers(): PeerConnection[];
    getPeerCount(): number;
    getBandwidthStats(): BandwidthStats;
    getNetworkInfo(): {
        nodeId: string;
        port: number;
        isListening: boolean;
        connectedPeers: number;
        maxPeers: number;
        averageLatency: number;
    };
    shutdown(): Promise<void>;
}
//# sourceMappingURL=P2PNetworking.d.ts.map