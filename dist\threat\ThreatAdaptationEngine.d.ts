export interface ThreatSignature {
    id: string;
    name: string;
    type: 'timing_analysis' | 'size_analysis' | 'pattern_analysis' | 'dpi_inspection' | 'behavioral_analysis';
    indicators: string[];
    severity: 'low' | 'medium' | 'high' | 'critical';
    countermeasures: string[];
    lastSeen: number;
    confidence: number;
}
export interface ThreatIntelligence {
    source: string;
    timestamp: number;
    threats: ThreatSignature[];
    version: string;
    checksum: string;
}
export interface AdaptationStrategy {
    id: string;
    name: string;
    triggers: string[];
    actions: AdaptationAction[];
    priority: number;
    effectiveness: number;
    lastUsed: number;
}
export interface AdaptationAction {
    type: 'increase_obfuscation' | 'change_pattern' | 'enable_countermeasure' | 'modify_timing' | 'switch_profile';
    parameters: Record<string, any>;
    duration: number;
}
export interface ThreatDetection {
    timestamp: number;
    threatId: string;
    confidence: number;
    source: 'network' | 'timing' | 'pattern' | 'behavioral';
    details: Record<string, any>;
}
export declare class ThreatAdaptationEngine {
    private threatDatabase;
    private adaptationStrategies;
    private activeDetections;
    private intelligenceFeeds;
    private adaptationHistory;
    private monitoringInterval;
    private updateInterval;
    constructor();
    initialize(): Promise<void>;
    private initializeThreatDatabase;
    private initializeAdaptationStrategies;
    private setupIntelligenceFeeds;
    private loadThreatIntelligence;
    private fetchThreatIntelligence;
    private processThreatIntelligence;
    private startThreatMonitoring;
    private startIntelligenceUpdates;
    private performThreatDetection;
    private detectTimingAnalysis;
    private detectSizeAnalysis;
    private detectPatternAnalysis;
    private detectBehavioralAnalysis;
    private evaluateAdaptationNeed;
    private shouldAdapt;
    private triggerAdaptation;
    private isStrategyApplicable;
    private executeAdaptationStrategy;
    private executeAdaptationAction;
    private increaseObfuscation;
    private changePattern;
    private enableCountermeasure;
    private modifyTiming;
    private switchProfile;
    getThreatStatus(): {
        activeThreats: number;
        totalSignatures: number;
        lastAdaptation: number;
        adaptationCount: number;
        threatLevel: string;
    };
    private calculateThreatLevel;
    getAdaptationHistory(): Array<{
        timestamp: number;
        strategy: string;
        effectiveness: number;
    }>;
    destroy(): void;
}
//# sourceMappingURL=ThreatAdaptationEngine.d.ts.map