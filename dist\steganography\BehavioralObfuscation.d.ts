export interface BehavioralProfile {
    name: string;
    readingSpeed: number;
    scrollPattern: 'fast' | 'medium' | 'slow' | 'erratic';
    clickFrequency: number;
    typingSpeed: number;
    pauseFrequency: number;
    tabSwitchingRate: number;
    searchBehavior: 'focused' | 'exploratory' | 'random';
}
export interface MouseMovementPattern {
    velocity: number;
    acceleration: number;
    jitter: number;
    pauseProbability: number;
}
export declare class BehavioralObfuscation {
    private currentProfile;
    private profiles;
    private mousePattern;
    private behaviorInterval;
    private activityLog;
    private isActive;
    constructor();
    initialize(): Promise<void>;
    private initializeBehavioralProfiles;
    private selectRandomProfile;
    private generateMousePattern;
    private startBehavioralSimulation;
    private calculateNextActionDelay;
    private executeBehavioralAction;
    private getWeightedActions;
    private getScrollWeight;
    private getMouseWeight;
    private getTypingWeight;
    private getReadingWeight;
    private getTabWeight;
    private getSearchWeight;
    private getPauseWeight;
    private selectWeightedAction;
    private simulateScrolling;
    private calculateScrollAmount;
    private calculateScrollDuration;
    private simulateMouseMovement;
    private generateMouseMovements;
    private simulateTyping;
    private generateTypingText;
    private calculateTypingDelay;
    private simulateReading;
    private calculateReadingDuration;
    private simulateTabInteraction;
    private simulateSearchBehavior;
    private simulateFocusedSearch;
    private simulateExploratorySearch;
    private simulateRandomSearch;
    private simulatePause;
    private calculatePauseDuration;
    private startProfileRotation;
    private rotateProfile;
    private logActivity;
    getCurrentProfile(): BehavioralProfile;
    getActivityStatistics(): {
        totalActions: number;
        avgActionDuration: number;
        actionsPerHour: number;
        profileName: string;
    };
    destroy(): void;
}
//# sourceMappingURL=BehavioralObfuscation.d.ts.map