"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BehavioralObfuscation = void 0;
const electron_1 = require("electron");
class BehavioralObfuscation {
    constructor() {
        this.behaviorInterval = null;
        this.activityLog = [];
        this.isActive = true;
        this.profiles = this.initializeBehavioralProfiles();
        this.currentProfile = this.selectRandomProfile();
        this.mousePattern = this.generateMousePattern();
    }
    async initialize() {
        this.startBehavioralSimulation();
        this.startProfileRotation();
        console.log(`Behavioral obfuscation initialized with profile: ${this.currentProfile.name}`);
    }
    initializeBehavioralProfiles() {
        return [
            {
                name: 'Casual Reader',
                readingSpeed: 200,
                scrollPattern: 'medium',
                clickFrequency: 15,
                typingSpeed: 40,
                pauseFrequency: 8,
                tabSwitchingRate: 12,
                searchBehavior: 'exploratory'
            },
            {
                name: 'Power User',
                readingSpeed: 350,
                scrollPattern: 'fast',
                clickFrequency: 45,
                typingSpeed: 80,
                pauseFrequency: 4,
                tabSwitchingRate: 30,
                searchBehavior: 'focused'
            },
            {
                name: 'Researcher',
                readingSpeed: 180,
                scrollPattern: 'slow',
                clickFrequency: 8,
                typingSpeed: 60,
                pauseFrequency: 15,
                tabSwitchingRate: 6,
                searchBehavior: 'focused'
            },
            {
                name: 'Social Media User',
                readingSpeed: 120,
                scrollPattern: 'fast',
                clickFrequency: 60,
                typingSpeed: 35,
                pauseFrequency: 3,
                tabSwitchingRate: 25,
                searchBehavior: 'random'
            },
            {
                name: 'Elderly User',
                readingSpeed: 100,
                scrollPattern: 'slow',
                clickFrequency: 5,
                typingSpeed: 20,
                pauseFrequency: 20,
                tabSwitchingRate: 3,
                searchBehavior: 'exploratory'
            },
            {
                name: 'Mobile-like User',
                readingSpeed: 150,
                scrollPattern: 'erratic',
                clickFrequency: 30,
                typingSpeed: 25,
                pauseFrequency: 10,
                tabSwitchingRate: 15,
                searchBehavior: 'random'
            }
        ];
    }
    selectRandomProfile() {
        return this.profiles[Math.floor(Math.random() * this.profiles.length)];
    }
    generateMousePattern() {
        return {
            velocity: 50 + Math.random() * 100, // 50-150 pixels/second
            acceleration: 10 + Math.random() * 20, // 10-30 pixels/second²
            jitter: Math.random() * 5, // 0-5 pixels random movement
            pauseProbability: 0.1 + Math.random() * 0.2 // 10-30% chance of pause
        };
    }
    startBehavioralSimulation() {
        // Start the main behavioral simulation loop
        this.behaviorInterval = setInterval(() => {
            this.executeBehavioralAction();
        }, this.calculateNextActionDelay());
    }
    calculateNextActionDelay() {
        // Calculate delay based on current profile
        const baseDelay = 60000 / this.currentProfile.clickFrequency; // Convert clicks/min to ms
        const randomFactor = 0.5 + Math.random(); // 0.5x to 1.5x variation
        return Math.floor(baseDelay * randomFactor);
    }
    executeBehavioralAction() {
        const actions = [
            () => this.simulateScrolling(),
            () => this.simulateMouseMovement(),
            () => this.simulateTyping(),
            () => this.simulateReading(),
            () => this.simulateTabInteraction(),
            () => this.simulateSearchBehavior(),
            () => this.simulatePause()
        ];
        // Weight actions based on profile
        const weightedActions = this.getWeightedActions(actions);
        const selectedAction = this.selectWeightedAction(weightedActions);
        const startTime = Date.now();
        selectedAction();
        const duration = Date.now() - startTime;
        this.logActivity(selectedAction.name, duration);
    }
    getWeightedActions(actions) {
        return [
            { action: actions[0], weight: this.getScrollWeight() },
            { action: actions[1], weight: this.getMouseWeight() },
            { action: actions[2], weight: this.getTypingWeight() },
            { action: actions[3], weight: this.getReadingWeight() },
            { action: actions[4], weight: this.getTabWeight() },
            { action: actions[5], weight: this.getSearchWeight() },
            { action: actions[6], weight: this.getPauseWeight() }
        ];
    }
    getScrollWeight() {
        switch (this.currentProfile.scrollPattern) {
            case 'fast': return 0.3;
            case 'medium': return 0.2;
            case 'slow': return 0.1;
            case 'erratic': return 0.4;
            default: return 0.2;
        }
    }
    getMouseWeight() {
        return Math.min(0.3, this.currentProfile.clickFrequency / 100);
    }
    getTypingWeight() {
        return Math.min(0.2, this.currentProfile.typingSpeed / 200);
    }
    getReadingWeight() {
        return Math.min(0.4, this.currentProfile.readingSpeed / 300);
    }
    getTabWeight() {
        return Math.min(0.15, this.currentProfile.tabSwitchingRate / 50);
    }
    getSearchWeight() {
        return this.currentProfile.searchBehavior === 'focused' ? 0.1 : 0.05;
    }
    getPauseWeight() {
        return Math.min(0.3, this.currentProfile.pauseFrequency / 30);
    }
    selectWeightedAction(weightedActions) {
        const totalWeight = weightedActions.reduce((sum, item) => sum + item.weight, 0);
        let random = Math.random() * totalWeight;
        for (const item of weightedActions) {
            random -= item.weight;
            if (random <= 0) {
                return item.action;
            }
        }
        return weightedActions[0].action; // Fallback
    }
    simulateScrolling() {
        const windows = electron_1.BrowserWindow.getAllWindows();
        if (windows.length === 0)
            return;
        const window = windows[0];
        const scrollAmount = this.calculateScrollAmount();
        const scrollDirection = Math.random() > 0.8 ? -1 : 1; // 20% chance to scroll up
        window.webContents.executeJavaScript(`
            (function() {
                const scrollDistance = ${scrollAmount * scrollDirection};
                const duration = ${this.calculateScrollDuration()};
                const startTime = Date.now();
                const startPosition = window.pageYOffset;
                
                function smoothScroll() {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    const easeProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI);
                    
                    window.scrollTo(0, startPosition + scrollDistance * easeProgress);
                    
                    if (progress < 1) {
                        requestAnimationFrame(smoothScroll);
                    }
                }
                
                smoothScroll();
            })();
        `).catch(() => {
            // Ignore errors if page doesn't support scrolling
        });
    }
    calculateScrollAmount() {
        switch (this.currentProfile.scrollPattern) {
            case 'fast': return 200 + Math.random() * 400;
            case 'medium': return 100 + Math.random() * 200;
            case 'slow': return 50 + Math.random() * 100;
            case 'erratic': return 20 + Math.random() * 600;
            default: return 150;
        }
    }
    calculateScrollDuration() {
        switch (this.currentProfile.scrollPattern) {
            case 'fast': return 200 + Math.random() * 300;
            case 'medium': return 400 + Math.random() * 400;
            case 'slow': return 800 + Math.random() * 600;
            case 'erratic': return 100 + Math.random() * 800;
            default: return 500;
        }
    }
    simulateMouseMovement() {
        // Simulate realistic mouse movement patterns
        const windows = electron_1.BrowserWindow.getAllWindows();
        if (windows.length === 0)
            return;
        const window = windows[0];
        window.webContents.executeJavaScript(`
            (function() {
                const movements = ${this.generateMouseMovements()};
                let index = 0;
                
                function executeMovement() {
                    if (index >= movements.length) return;
                    
                    const movement = movements[index];
                    const event = new MouseEvent('mousemove', {
                        clientX: movement.x,
                        clientY: movement.y,
                        bubbles: true
                    });
                    
                    document.dispatchEvent(event);
                    index++;
                    
                    setTimeout(executeMovement, movement.delay);
                }
                
                executeMovement();
            })();
        `).catch(() => {
            // Ignore errors
        });
    }
    generateMouseMovements() {
        const movements = [];
        const numMovements = 5 + Math.floor(Math.random() * 10);
        let currentX = Math.random() * 800;
        let currentY = Math.random() * 600;
        for (let i = 0; i < numMovements; i++) {
            const deltaX = (Math.random() - 0.5) * 200;
            const deltaY = (Math.random() - 0.5) * 200;
            currentX = Math.max(0, Math.min(800, currentX + deltaX));
            currentY = Math.max(0, Math.min(600, currentY + deltaY));
            movements.push({
                x: Math.floor(currentX),
                y: Math.floor(currentY),
                delay: 50 + Math.random() * 200
            });
        }
        return JSON.stringify(movements);
    }
    simulateTyping() {
        // Simulate typing behavior with realistic patterns
        const windows = electron_1.BrowserWindow.getAllWindows();
        if (windows.length === 0)
            return;
        const window = windows[0];
        const typingText = this.generateTypingText();
        window.webContents.executeJavaScript(`
            (function() {
                const text = "${typingText}";
                const activeElement = document.activeElement;
                
                if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
                    let index = 0;
                    
                    function typeCharacter() {
                        if (index >= text.length) return;
                        
                        const char = text[index];
                        const currentValue = activeElement.value || '';
                        activeElement.value = currentValue + char;
                        
                        // Trigger input event
                        const event = new Event('input', { bubbles: true });
                        activeElement.dispatchEvent(event);
                        
                        index++;
                        const delay = ${this.calculateTypingDelay()};
                        setTimeout(typeCharacter, delay);
                    }
                    
                    typeCharacter();
                }
            })();
        `).catch(() => {
            // Ignore errors
        });
    }
    generateTypingText() {
        const phrases = [
            'hello world',
            'test search',
            'example query',
            'sample text',
            'random input'
        ];
        return phrases[Math.floor(Math.random() * phrases.length)];
    }
    calculateTypingDelay() {
        const baseDelay = 60000 / this.currentProfile.typingSpeed; // Convert CPM to ms per character
        const randomFactor = 0.5 + Math.random(); // 0.5x to 1.5x variation
        return Math.floor(baseDelay * randomFactor);
    }
    simulateReading() {
        // Simulate reading behavior by pausing and occasional scrolling
        const readingDuration = this.calculateReadingDuration();
        setTimeout(() => {
            // Occasionally scroll while reading
            if (Math.random() < 0.3) {
                this.simulateScrolling();
            }
        }, readingDuration / 2);
    }
    calculateReadingDuration() {
        // Estimate reading time based on profile
        const wordsOnScreen = 100 + Math.random() * 200; // Estimate 100-300 words visible
        const readingTimeMs = (wordsOnScreen / this.currentProfile.readingSpeed) * 60000;
        return Math.floor(readingTimeMs);
    }
    simulateTabInteraction() {
        // Simulate tab switching or opening new tabs
        if (Math.random() < 0.7) {
            // Switch to existing tab
            console.log('Simulating tab switch');
        }
        else {
            // Open new tab
            console.log('Simulating new tab creation');
        }
    }
    simulateSearchBehavior() {
        // Simulate search behavior based on profile
        const searchActions = {
            'focused': () => this.simulateFocusedSearch(),
            'exploratory': () => this.simulateExploratorySearch(),
            'random': () => this.simulateRandomSearch()
        };
        const action = searchActions[this.currentProfile.searchBehavior];
        if (action) {
            action();
        }
    }
    simulateFocusedSearch() {
        // Simulate focused, goal-oriented search behavior
        console.log('Simulating focused search behavior');
    }
    simulateExploratorySearch() {
        // Simulate exploratory browsing with related topics
        console.log('Simulating exploratory search behavior');
    }
    simulateRandomSearch() {
        // Simulate random, unfocused browsing
        console.log('Simulating random search behavior');
    }
    simulatePause() {
        // Simulate natural pauses in browsing
        const pauseDuration = this.calculatePauseDuration();
        console.log(`Simulating pause for ${pauseDuration}ms`);
        // During pause, reduce activity
        this.isActive = false;
        setTimeout(() => {
            this.isActive = true;
        }, pauseDuration);
    }
    calculatePauseDuration() {
        // Calculate pause duration based on profile
        const basePause = 60000 / this.currentProfile.pauseFrequency; // Convert pauses/hour to ms
        const randomFactor = 0.3 + Math.random() * 1.4; // 0.3x to 1.7x variation
        return Math.floor(basePause * randomFactor);
    }
    startProfileRotation() {
        // Rotate behavioral profile every 30-60 minutes
        setInterval(() => {
            this.rotateProfile();
        }, (30 + Math.random() * 30) * 60 * 1000);
    }
    rotateProfile() {
        const oldProfile = this.currentProfile.name;
        this.currentProfile = this.selectRandomProfile();
        this.mousePattern = this.generateMousePattern();
        console.log(`Behavioral profile rotated from ${oldProfile} to ${this.currentProfile.name}`);
    }
    logActivity(action, duration) {
        this.activityLog.push({
            action,
            timestamp: Date.now(),
            duration
        });
        // Keep only last hour of activity
        const oneHourAgo = Date.now() - 3600000;
        this.activityLog = this.activityLog.filter(log => log.timestamp > oneHourAgo);
    }
    getCurrentProfile() {
        return { ...this.currentProfile };
    }
    getActivityStatistics() {
        const recentActions = this.activityLog.filter(log => Date.now() - log.timestamp < 3600000 // Last hour
        );
        const totalActions = recentActions.length;
        const avgActionDuration = totalActions > 0
            ? recentActions.reduce((sum, log) => sum + log.duration, 0) / totalActions
            : 0;
        return {
            totalActions,
            avgActionDuration,
            actionsPerHour: totalActions,
            profileName: this.currentProfile.name
        };
    }
    destroy() {
        if (this.behaviorInterval) {
            clearInterval(this.behaviorInterval);
            this.behaviorInterval = null;
        }
    }
}
exports.BehavioralObfuscation = BehavioralObfuscation;
//# sourceMappingURL=BehavioralObfuscation.js.map