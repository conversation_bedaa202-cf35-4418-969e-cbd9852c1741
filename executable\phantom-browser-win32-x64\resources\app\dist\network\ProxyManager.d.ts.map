{"version": 3, "file": "ProxyManager.d.ts", "sourceRoot": "", "sources": ["../../src/network/ProxyManager.ts"], "names": [], "mappings": "AAIA,MAAM,WAAW,WAAW;IACxB,IAAI,EAAE,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACxD,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,SAAS;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,SAAS,GAAG,WAAW,GAAG,OAAO,CAAC;IAC5C,WAAW,EAAE;QACT,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;KACvB,CAAC;IACF,OAAO,EAAE,OAAO,CAAC;CACpB;AAED,qBAAa,YAAY;IACrB,OAAO,CAAC,YAAY,CAA4B;IAChD,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,eAAe,CAAkB;IACzC,OAAO,CAAC,gBAAgB,CAA+B;IACvD,OAAO,CAAC,SAAS,CAA0B;;IAMrC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC,OAAO,CAAC,wBAAwB;YA0BlB,aAAa;IAqBrB,QAAQ,CAAC,MAAM,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAsC5C,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAO3B,SAAS,CAAC,MAAM,EAAE,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;YAcxC,cAAc;YA0Bd,aAAa;IAuBrB,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAuBlC,mBAAmB,CAAC,eAAe,GAAE,MAAW,GAAG,IAAI;IAcvD,oBAAoB,IAAI,IAAI;IAW5B,QAAQ,CAAC,MAAM,EAAE,WAAW,GAAG,IAAI;IAInC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI;IAI7C,YAAY,IAAI,WAAW,EAAE;IAI7B,eAAe,IAAI,WAAW,GAAG,IAAI;IAI/B,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IAkBlC,uBAAuB,IAAI,OAAO,CAAC,IAAI,CAAC;IA2BxC,UAAU,CAAC,MAAM,EAAE,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAQ5C,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;IAMpC,YAAY,IAAI;QAAE,SAAS,EAAE,OAAO,CAAC;QAAC,MAAM,CAAC,EAAE,SAAS,CAAA;KAAE;IAO1D,OAAO,IAAI,IAAI;CAMlB"}